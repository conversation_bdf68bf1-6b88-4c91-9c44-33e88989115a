/**
 * 验证滑动修复效果的测试脚本
 * 验证：词库滑动只在双击激活时触发一次，鼠标移动不会重复触发
 */

console.log('🔧 开始验证滑动修复效果...');

const fs = require('fs');
const path = require('path');

// 检查WordLibraryManager.tsx中的修复
const wordLibraryManagerPath = path.join(__dirname, '../components/WordLibraryManager.tsx');
const wordLibraryManagerContent = fs.readFileSync(wordLibraryManagerPath, 'utf8');

console.log('\n📋 检查滑动逻辑修复:');

// 检查是否添加了滑动状态跟踪
if (wordLibraryManagerContent.includes('hasScrolledForCurrentActivation')) {
  console.log('✅ 已添加滑动状态跟踪');
} else {
  console.log('❌ 缺少滑动状态跟踪');
}

// 检查是否添加了激活状态跟踪
if (wordLibraryManagerContent.includes('lastActiveLibraryRef')) {
  console.log('✅ 已添加激活状态跟踪');
} else {
  console.log('❌ 缺少激活状态跟踪');
}

// 检查是否有新激活检查逻辑
if (wordLibraryManagerContent.includes('isNewActivation')) {
  console.log('✅ 已添加新激活检查逻辑');
} else {
  console.log('❌ 缺少新激活检查逻辑');
}

// 检查useEffect依赖项
if (wordLibraryManagerContent.includes('[isActiveLibrary, matchedLibrary, libraryKey, isElementInViewport]')) {
  console.log('✅ useEffect依赖项正确');
} else {
  console.log('❌ useEffect依赖项可能有问题');
}

console.log('\n🎉 验证完成！');

// 输出修复总结
console.log('\n📊 修复总结:');
console.log('1. ✅ 添加滑动状态跟踪，防止重复滑动');
console.log('2. ✅ 添加激活状态跟踪，区分新旧激活');
console.log('3. ✅ 只在新激活且元素不在可视区域时滑动');
console.log('4. ✅ 鼠标移动等操作不会触发重复滑动');

console.log('\n🧪 手动测试建议:');
console.log('1. 双击激活填词模式，观察控制台日志');
console.log('2. 移动鼠标，确认不会重复触发滑动');
console.log('3. 切换到不同词库，确认只滑动一次');
console.log('4. 在同一词库内操作，确认不会重复滑动');
