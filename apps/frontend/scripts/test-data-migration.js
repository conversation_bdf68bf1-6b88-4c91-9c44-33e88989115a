/**
 * 数据迁移测试脚本
 * 验证旧版本数据格式的兼容性处理
 */

console.log('🔄 开始测试数据迁移逻辑...\n');

// 模拟旧版本数据格式（Map被序列化为普通对象）
const oldVersionData = {
  libraries: {
    'black-1': {
      key: 'black-1',
      color: 'black',
      level: 1,
      words: [
        { id: '1', text: '主题', color: 'black', level: 1 }
      ],
      collapsed: false,
      lastUpdated: new Date()
    },
    'red-1': {
      key: 'red-1',
      color: 'red',
      level: 1,
      words: [
        { id: '2', text: '集合', color: 'red', level: 1 }
      ],
      collapsed: false,
      lastUpdated: new Date()
    }
  },
  duplicateWords: {
    '主题': ['black-1', 'red-1']
  },
  wordHighlightColors: {
    '主题': '#ffeb3b'
  },
  usedHighlightColors: {
    0: '#ffeb3b',
    1: '#ff9800'
  },
  activeLibrary: null,
  isLoading: false,
  lastSyncTime: null
};

// 模拟新版本数据格式（数组格式）
const newVersionData = {
  libraries: [
    ['black-1', {
      key: 'black-1',
      color: 'black',
      level: 1,
      words: [{ id: '1', text: '主题', color: 'black', level: 1 }],
      collapsed: false,
      lastUpdated: new Date()
    }]
  ],
  duplicateWords: [
    ['主题', ['black-1', 'red-1']]
  ],
  wordHighlightColors: [
    ['主题', '#ffeb3b']
  ],
  usedHighlightColors: ['#ffeb3b', '#ff9800'],
  activeLibrary: null,
  isLoading: false,
  lastSyncTime: null
};

// 测试数据迁移函数
function testDataMigration(data, dataType) {
  console.log(`=== 测试${dataType}数据迁移 ===`);
  
  try {
    // 模拟onRehydrateStorage逻辑
    const state = { ...data };
    
    // 处理libraries字段
    if (Array.isArray(state.libraries)) {
      console.log('✅ 检测到数组格式libraries');
      state.libraries = new Map(state.libraries);
    } else if (state.libraries && typeof state.libraries === 'object' && !(state.libraries instanceof Map)) {
      console.log('✅ 检测到对象格式libraries，正在转换...');
      const entries = Object.entries(state.libraries);
      state.libraries = new Map(entries);
    } else if (!state.libraries) {
      state.libraries = new Map();
    }
    
    // 处理duplicateWords字段
    if (Array.isArray(state.duplicateWords)) {
      console.log('✅ 检测到数组格式duplicateWords');
      state.duplicateWords = new Map(state.duplicateWords);
    } else if (state.duplicateWords && typeof state.duplicateWords === 'object' && !(state.duplicateWords instanceof Map)) {
      console.log('✅ 检测到对象格式duplicateWords，正在转换...');
      const entries = Object.entries(state.duplicateWords);
      state.duplicateWords = new Map(entries);
    } else if (!state.duplicateWords) {
      state.duplicateWords = new Map();
    }
    
    // 处理wordHighlightColors字段
    if (Array.isArray(state.wordHighlightColors)) {
      console.log('✅ 检测到数组格式wordHighlightColors');
      state.wordHighlightColors = new Map(state.wordHighlightColors);
    } else if (state.wordHighlightColors && typeof state.wordHighlightColors === 'object' && !(state.wordHighlightColors instanceof Map)) {
      console.log('✅ 检测到对象格式wordHighlightColors，正在转换...');
      const entries = Object.entries(state.wordHighlightColors);
      state.wordHighlightColors = new Map(entries);
    } else if (!state.wordHighlightColors) {
      state.wordHighlightColors = new Map();
    }
    
    // 处理usedHighlightColors字段
    if (Array.isArray(state.usedHighlightColors)) {
      console.log('✅ 检测到数组格式usedHighlightColors');
      state.usedHighlightColors = new Set(state.usedHighlightColors);
    } else if (state.usedHighlightColors && typeof state.usedHighlightColors === 'object' && !(state.usedHighlightColors instanceof Set)) {
      console.log('✅ 检测到对象格式usedHighlightColors，正在转换...');
      const values = Object.values(state.usedHighlightColors);
      state.usedHighlightColors = new Set(values);
    } else if (!state.usedHighlightColors) {
      state.usedHighlightColors = new Set();
    }
    
    // 验证转换结果
    const librariesIsMap = state.libraries instanceof Map;
    const duplicateWordsIsMap = state.duplicateWords instanceof Map;
    const wordHighlightColorsIsMap = state.wordHighlightColors instanceof Map;
    const usedHighlightColorsIsSet = state.usedHighlightColors instanceof Set;
    
    console.log('📊 转换结果验证:');
    console.log(`  libraries是Map: ${librariesIsMap}`);
    console.log(`  duplicateWords是Map: ${duplicateWordsIsMap}`);
    console.log(`  wordHighlightColors是Map: ${wordHighlightColorsIsMap}`);
    console.log(`  usedHighlightColors是Set: ${usedHighlightColorsIsSet}`);
    
    // 测试Map方法是否可用
    if (librariesIsMap) {
      const testGet = state.libraries.get('black-1');
      console.log(`  libraries.get()方法可用: ${testGet !== undefined}`);
    }
    
    const allValid = librariesIsMap && duplicateWordsIsMap && wordHighlightColorsIsMap && usedHighlightColorsIsSet;
    
    if (allValid) {
      console.log(`✅ PASS: ${dataType}数据迁移成功\n`);
    } else {
      console.log(`❌ FAIL: ${dataType}数据迁移失败\n`);
    }
    
    return allValid;
    
  } catch (error) {
    console.log(`❌ ERROR: ${dataType}数据迁移出错:`, error.message);
    return false;
  }
}

// 运行测试
function runMigrationTests() {
  const oldResult = testDataMigration(oldVersionData, '旧版本');
  const newResult = testDataMigration(newVersionData, '新版本');
  
  console.log('📊 数据迁移测试总结');
  console.log('='.repeat(50));
  console.log(`旧版本数据兼容性: ${oldResult ? '✅ 通过' : '❌ 失败'}`);
  console.log(`新版本数据兼容性: ${newResult ? '✅ 通过' : '❌ 失败'}`);
  
  if (oldResult && newResult) {
    console.log('🎉 所有数据格式都能正确迁移！');
    console.log('💡 用户无需清理浏览器数据即可正常使用');
  } else {
    console.log('⚠️ 数据迁移存在问题，需要进一步修复');
  }
}

// 运行测试
runMigrationTests();
