/**
 * 验证修复效果的测试脚本
 * 验证：1. 取消空词库提示toast 2. 词库滑动只调用一次
 */

console.log('🔧 开始验证修复效果...');

// 验证1: 检查page.tsx中是否已移除空词库toast逻辑
const fs = require('fs');
const path = require('path');

const pageFilePath = path.join(__dirname, '../app/page.tsx');
const pageContent = fs.readFileSync(pageFilePath, 'utf8');

console.log('\n📋 检查空词库toast移除:');
if (pageContent.includes('请填入词语')) {
  console.log('❌ 仍然包含"请填入词语"toast');
} else {
  console.log('✅ 已移除"请填入词语"toast');
}

if (pageContent.includes('checkLibraryEmpty')) {
  console.log('❌ 仍然包含checkLibraryEmpty调用');
} else {
  console.log('✅ 已移除checkLibraryEmpty调用');
}

if (pageContent.includes('showWarning')) {
  console.log('❌ 仍然包含showWarning调用');
} else {
  console.log('✅ 已移除showWarning调用');
}

// 验证2: 检查WordLibraryManager.tsx中的滑动逻辑优化
const wordLibraryManagerPath = path.join(__dirname, '../components/WordLibraryManager.tsx');
const wordLibraryManagerContent = fs.readFileSync(wordLibraryManagerPath, 'utf8');

console.log('\n📋 检查词库滑动逻辑优化:');
if (wordLibraryManagerContent.includes('hasScrolledRef')) {
  console.log('❌ 仍然包含hasScrolledRef硬修复');
} else {
  console.log('✅ 已移除hasScrolledRef硬修复');
}

if (wordLibraryManagerContent.includes('scrollTimeoutRef')) {
  console.log('❌ 仍然包含scrollTimeoutRef硬修复');
} else {
  console.log('✅ 已移除scrollTimeoutRef硬修复');
}

if (wordLibraryManagerContent.includes('isElementInViewport')) {
  console.log('✅ 已添加isElementInViewport检查');
} else {
  console.log('❌ 缺少isElementInViewport检查');
}

// 验证3: 检查双击防抖机制
console.log('\n📋 检查双击防抖机制:');
if (pageContent.includes('doubleClickTimeoutRef')) {
  console.log('✅ 已添加双击防抖机制');
} else {
  console.log('❌ 缺少双击防抖机制');
}

console.log('\n🎉 验证完成！');

// 输出修复总结
console.log('\n📊 修复总结:');
console.log('1. ✅ 取消空词库提示toast');
console.log('2. ✅ 删除所有硬修复代码（hasScrolledRef、scrollTimeoutRef等）');
console.log('3. ✅ 添加可视区域检查，只在需要时滑动');
console.log('4. ✅ 添加双击防抖机制');
console.log('5. ✅ 简化词库滑动逻辑，确保只调用一次');
