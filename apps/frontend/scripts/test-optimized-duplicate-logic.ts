/**
 * 优化后的查重逻辑测试脚本
 * 🎯 测试目标：验证全局词语索引和分离的验证逻辑
 * 📦 测试范围：性能优化、输入验证、跨词库检测
 */

import { create } from 'zustand';
import { enableMapSet, produce } from 'immer';
import type { WordLibraryState, WordLibraryKey } from '../core/matrix/MatrixTypes';
import { 
  createInitialWordLibraryState,
  createWordEntry,
  validateInputBeforeSubmit,
  detectCrossLibraryDuplicates,
  addWordToGlobalIndex,
  removeWordFromGlobalIndex,
  isSameLibraryDuplicate,
  isCrossLibraryDuplicate,
  getWordLibraries,
  buildGlobalWordIndex
} from '../core/wordLibrary/WordLibraryCore';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// 简化的Store接口用于测试
interface TestStore extends WordLibraryState {
  validateInput: (libraryKey: WordLibraryKey, text: string) => { isValid: boolean; errors: string[] };
  checkCrossLibraryDuplicate: (text: string) => { isDuplicate: boolean; duplicateLibraries: WordLibraryKey[] };
  addWordDirect: (libraryKey: WordLibraryKey, text: string) => boolean;
  removeWordDirect: (libraryKey: WordLibraryKey, wordText: string) => boolean;
}

// 创建测试Store
const createTestStore = () => create<TestStore>()((set, get) => ({
  ...createInitialWordLibraryState(),
  
  validateInput: (libraryKey: WordLibraryKey, text: string) => {
    const state = get();
    return validateInputBeforeSubmit(text, libraryKey, state.globalWordIndex);
  },
  
  checkCrossLibraryDuplicate: (text: string) => {
    const state = get();
    return detectCrossLibraryDuplicates(text, state.globalWordIndex);
  },
  
  addWordDirect: (libraryKey: WordLibraryKey, text: string) => {
    let success = false;
    set(produce((draft) => {
      const library = draft.libraries.get(libraryKey);
      if (library) {
        const wordEntry = createWordEntry(text, library.color, library.level);
        library.words.push(wordEntry);
        library.lastUpdated = new Date();
        
        // 同步更新全局索引
        addWordToGlobalIndex(draft.globalWordIndex, text, libraryKey);
        success = true;
      }
    }));
    return success;
  },
  
  removeWordDirect: (libraryKey: WordLibraryKey, wordText: string) => {
    let success = false;
    set(produce((draft) => {
      const library = draft.libraries.get(libraryKey);
      if (library) {
        const wordIndex = library.words.findIndex(word => word.text === wordText);
        if (wordIndex !== -1) {
          library.words.splice(wordIndex, 1);
          library.lastUpdated = new Date();
          
          // 同步更新全局索引
          removeWordFromGlobalIndex(draft.globalWordIndex, wordText, libraryKey);
          success = true;
        }
      }
    }));
    return success;
  }
}));

// 性能测试函数
function performanceTest() {
  console.log('🚀 性能测试开始\n');
  
  const useStore = createTestStore();
  const state = useStore.getState();
  
  // 添加大量测试数据
  const testWords = ['测试', '开发', '编程', '代码', '项目', '功能', '优化', '性能'];
  const libraries = ['black-1', 'red-1', 'blue-1', 'green-1', 'yellow-1'] as WordLibraryKey[];
  
  console.log('📊 添加测试数据...');
  libraries.forEach((libraryKey, libIndex) => {
    testWords.forEach((word, wordIndex) => {
      // 创建一些重复词语用于测试
      const testWord = libIndex === 0 ? word : `${word}${libIndex}`;
      if (wordIndex < 3) {
        // 前3个词语在所有词库中都有（用于测试跨词库重复）
        useStore.getState().addWordDirect(libraryKey, testWords[wordIndex]);
      } else {
        // 其他词语只在当前词库中
        useStore.getState().addWordDirect(libraryKey, testWord);
      }
    });
  });
  
  console.log('✅ 测试数据添加完成');
  console.log(`总词语数: ${Array.from(state.libraries.values()).reduce((sum, lib) => sum + lib.words.length, 0)}`);
  console.log(`索引大小: ${state.globalWordIndex.size}`);
  
  // 性能测试：查找重复
  console.log('\n⏱️ 性能测试：查找重复');
  const startTime = performance.now();
  
  for (let i = 0; i < 1000; i++) {
    // 测试同一词库重复检查
    isSameLibraryDuplicate(state.globalWordIndex, '测试', 'black-1');
    
    // 测试跨词库重复检查
    isCrossLibraryDuplicate(state.globalWordIndex, '测试');
    
    // 测试获取词语所在词库
    getWordLibraries(state.globalWordIndex, '开发');
  }
  
  const endTime = performance.now();
  console.log(`1000次查重操作耗时: ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`平均每次操作: ${((endTime - startTime) / 1000).toFixed(4)}ms`);
}

// 功能测试
function functionalTest() {
  console.log('\n🧪 功能测试开始\n');
  
  const useStore = createTestStore();
  
  console.log('📋 测试1: 输入验证（阻止性）');
  console.log('---');
  
  // 测试格式错误
  let result1 = useStore.getState().validateInput('black-1', '短');
  console.log('格式错误（太短）:', !result1.isValid ? '✅ 正确阻止' : '❌ 错误允许');
  
  let result2 = useStore.getState().validateInput('black-1', 'test');
  console.log('格式错误（非中文）:', !result2.isValid ? '✅ 正确阻止' : '❌ 错误允许');
  
  // 添加一个词语
  useStore.getState().addWordDirect('black-1', '编程');
  
  // 测试同一词库重复
  let result3 = useStore.getState().validateInput('black-1', '编程');
  console.log('同一词库重复:', !result3.isValid ? '✅ 正确阻止' : '❌ 错误允许');
  console.log('错误信息:', result3.errors);
  
  // 测试跨词库不阻止
  let result4 = useStore.getState().validateInput('red-1', '编程');
  console.log('跨词库重复:', result4.isValid ? '✅ 正确允许' : '❌ 错误阻止');
  
  console.log('\n📋 测试2: 跨词库重复检测（提醒性）');
  console.log('---');
  
  // 添加跨词库重复词语
  useStore.getState().addWordDirect('red-1', '编程');
  useStore.getState().addWordDirect('blue-1', '编程');
  
  let crossCheck = useStore.getState().checkCrossLibraryDuplicate('编程');
  console.log('跨词库重复检测:', crossCheck.isDuplicate ? '✅ 正确检测' : '❌ 检测失败');
  console.log('重复词库:', crossCheck.duplicateLibraries);
  
  console.log('\n📋 测试3: 索引同步测试');
  console.log('---');
  
  const state = useStore.getState();
  console.log('添加前索引状态:');
  console.log('- "测试"所在词库:', getWordLibraries(state.globalWordIndex, '测试'));
  
  // 添加新词语
  useStore.getState().addWordDirect('green-1', '测试');
  console.log('添加后索引状态:');
  console.log('- "测试"所在词库:', getWordLibraries(state.globalWordIndex, '测试'));
  
  // 删除词语
  useStore.getState().removeWordDirect('green-1', '测试');
  console.log('删除后索引状态:');
  console.log('- "测试"所在词库:', getWordLibraries(state.globalWordIndex, '测试'));
  
  console.log('\n📋 测试4: 索引重建测试');
  console.log('---');
  
  // 重建索引
  const libraries = state.libraries;
  const rebuiltIndex = buildGlobalWordIndex(libraries);
  
  console.log('原索引大小:', state.globalWordIndex.size);
  console.log('重建索引大小:', rebuiltIndex.size);
  console.log('索引一致性:', state.globalWordIndex.size === rebuiltIndex.size ? '✅ 一致' : '❌ 不一致');
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始优化后的查重逻辑测试\n');
  
  try {
    performanceTest();
    functionalTest();
    
    console.log('\n✨ 所有测试完成');
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests();
}

export { runAllTests };
