/**
 * 用户反馈功能测试脚本
 * 🎯 测试目标：验证用户在输入被阻止时能收到清晰的提醒
 * 📦 测试范围：错误提示、用户体验、反馈机制
 */

import { create } from 'zustand';
import { enableMapSet, produce } from 'immer';
import type { WordLibraryState, WordLibraryKey } from '../core/matrix/MatrixTypes';
import { 
  createInitialWordLibraryState,
  createWordEntry,
  validateInputBeforeSubmit,
  detectCrossLibraryDuplicates,
  addWordToGlobalIndex,
  assignWordHighlightColor
} from '../core/wordLibrary/WordLibraryCore';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// 模拟用户输入场景的测试Store
interface TestStore extends WordLibraryState {
  validateInput: (libraryKey: WordLibraryKey, text: string) => { isValid: boolean; errors: string[] };
  addWord: (libraryKey: WordLibraryKey, text: string) => any;
  simulateUserInput: (libraryKey: WordLibraryKey, inputs: string[]) => void;
}

// 创建测试Store
const createTestStore = () => create<TestStore>()((set, get) => ({
  ...createInitialWordLibraryState(),
  
  validateInput: (libraryKey: WordLibraryKey, text: string) => {
    const state = get();
    return validateInputBeforeSubmit(text, libraryKey, state.globalWordIndex);
  },
  
  addWord: (libraryKey: WordLibraryKey, text: string) => {
    // 使用新的输入验证（阻止性）
    const inputValidation = get().validateInput(libraryKey, text);
    
    if (!inputValidation.isValid) {
      // 输入验证失败，直接返回错误，不录入数据
      return {
        isValid: false,
        errors: inputValidation.errors,
        isDuplicate: false,
        duplicateLibraries: []
      };
    }

    // 输入验证通过，直接录入数据
    const trimmedText = text.trim();
    let addedSuccessfully = false;

    set(produce((draft) => {
      const library = draft.libraries.get(libraryKey);
      if (library) {
        const wordEntry = createWordEntry(trimmedText, library.color, library.level);
        library.words.push(wordEntry);
        library.lastUpdated = new Date();
        
        // 同步更新全局索引
        addWordToGlobalIndex(draft.globalWordIndex, trimmedText, libraryKey);
        
        addedSuccessfully = true;
      }
    }));

    if (addedSuccessfully) {
      // 检查跨词库重复并更新高亮
      const crossLibraryCheck = detectCrossLibraryDuplicates(trimmedText, get().globalWordIndex);
      
      if (crossLibraryCheck.isDuplicate) {
        // 为跨词库重复词语分配高亮颜色
        const currentColor = get().wordHighlightColors.get(trimmedText);
        if (!currentColor) {
          const newColor = assignWordHighlightColor(get().usedHighlightColors);
          set(produce((draft) => {
            draft.wordHighlightColors.set(trimmedText, newColor);
            draft.usedHighlightColors.add(newColor);
          }));
        }
      }

      return {
        isValid: true,
        errors: [],
        isDuplicate: crossLibraryCheck.isDuplicate,
        duplicateLibraries: crossLibraryCheck.duplicateLibraries
      };
    }

    return {
      isValid: false,
      errors: ['添加词语失败'],
      isDuplicate: false,
      duplicateLibraries: []
    };
  },
  
  // 模拟用户输入场景
  simulateUserInput: (libraryKey: WordLibraryKey, inputs: string[]) => {
    console.log(`\n📝 模拟用户在${libraryKey}词库中输入: [${inputs.join(', ')}]`);
    
    const validWords: string[] = [];
    const errorWords: { word: string; error: string }[] = [];
    
    inputs.forEach(word => {
      const validation = get().validateInput(libraryKey, word);
      
      if (validation.isValid) {
        const result = get().addWord(libraryKey, word);
        if (result.isValid) {
          validWords.push(word);
          console.log(`  ✅ "${word}" - 添加成功${result.isDuplicate ? ' (跨词库重复，已高亮)' : ''}`);
        }
      } else {
        const errorMsg = validation.errors[0] || '输入无效';
        errorWords.push({ word, error: errorMsg });
        console.log(`  ❌ "${word}" - ${errorMsg}`);
      }
    });
    
    // 模拟UI反馈
    if (errorWords.length > 0) {
      if (errorWords.length === 1) {
        console.log(`\n💬 用户看到提示: "${errorWords[0].error}"`);
      } else {
        console.log(`\n💬 用户看到提示: "${errorWords.length}个词语输入无效，请检查格式或重复"`);
      }
    }
    
    if (validWords.length > 0) {
      console.log(`\n🎉 成功添加 ${validWords.length} 个词语`);
    }
  }
}));

// 用户反馈测试场景
function runUserFeedbackTests() {
  console.log('🚀 开始用户反馈功能测试\n');
  
  const useStore = createTestStore();
  
  console.log('📋 场景1: 同一词库重复输入');
  console.log('=' .repeat(50));
  
  // 先添加一个词语
  useStore.getState().addWord('black-1', '编程');
  console.log('✅ 预设: 在黑1词库中添加了"编程"');
  
  // 模拟用户再次输入相同词语
  useStore.getState().simulateUserInput('black-1', ['编程']);
  
  console.log('\n📋 场景2: 格式错误输入');
  console.log('=' .repeat(50));
  
  // 模拟各种格式错误
  useStore.getState().simulateUserInput('red-1', [
    '短',           // 太短
    'test',         // 非中文
    '测试！',       // 包含标点
    '这是一个很长的词语' // 太长
  ]);
  
  console.log('\n📋 场景3: 混合输入（有效+无效）');
  console.log('=' .repeat(50));
  
  // 模拟用户一次输入多个词语，有些有效有些无效
  useStore.getState().simulateUserInput('blue-1', [
    '开发',         // 有效
    '编程',         // 有效（跨词库重复）
    '短',           // 无效（太短）
    '测试',         // 有效
    'bug'           // 无效（非中文）
  ]);
  
  console.log('\n📋 场景4: 批量重复输入');
  console.log('=' .repeat(50));
  
  // 模拟用户输入多个重复词语
  useStore.getState().simulateUserInput('green-1', [
    '编程',         // 重复
    '开发',         // 重复
    '测试',         // 重复
    '新词语'        // 有效
  ]);
  
  console.log('\n📋 场景5: 空输入和边界情况');
  console.log('=' .repeat(50));
  
  // 模拟边界情况
  useStore.getState().simulateUserInput('yellow-1', [
    '',             // 空字符串
    '   ',          // 空白字符
    '正常',         // 正常词语
    '正常词语',     // 4个字符（边界）
    '正'            // 1个字符（边界）
  ]);
  
  console.log('\n📊 测试总结');
  console.log('=' .repeat(50));
  
  const state = useStore.getState();
  let totalWords = 0;
  
  console.log('各词库词语统计:');
  state.libraries.forEach((library, key) => {
    const count = library.words.length;
    totalWords += count;
    console.log(`  ${key}: ${count} 个词语`);
  });
  
  console.log(`\n总词语数: ${totalWords}`);
  console.log(`重复词语数: ${state.duplicateWords.size}`);
  console.log(`高亮颜色数: ${state.wordHighlightColors.size}`);
  
  // 显示重复词语的高亮情况
  if (state.wordHighlightColors.size > 0) {
    console.log('\n🎨 跨词库重复词语高亮:');
    state.wordHighlightColors.forEach((color, word) => {
      const libraries = state.duplicateWords.get(word) || [];
      console.log(`  "${word}": ${color} (出现在: ${libraries.join(', ')})`);
    });
  }
  
  console.log('\n✨ 用户反馈功能测试完成');
  console.log('\n💡 关键改进点:');
  console.log('  1. ❌ 同一词库重复 → 直接阻止 + 清晰提示');
  console.log('  2. 🎨 跨词库重复 → 允许录入 + 高亮提醒');
  console.log('  3. 📝 格式错误 → 阻止录入 + 具体错误信息');
  console.log('  4. 🔄 批量输入 → 分别处理 + 汇总反馈');
}

// 运行测试
if (require.main === module) {
  runUserFeedbackTests();
}

export { runUserFeedbackTests };
