/**
 * Bug修复验证测试脚本
 * 🎯 测试目标：验证Immer冻结对象错误和同一词库重复录入问题的修复
 * 📦 测试范围：输入验证、高亮颜色分配、删除操作
 */

import { create } from 'zustand';
import { enableMapSet, produce } from 'immer';
import type { WordLibraryState, WordLibraryKey } from '../core/matrix/MatrixTypes';
import { 
  createInitialWordLibraryState,
  createWordEntry,
  validateInputBeforeSubmit,
  detectCrossLibraryDuplicates,
  addWordToGlobalIndex,
  removeWordFromGlobalIndex,
  assignWordHighlightColor,
  getWordHighlightColor
} from '../core/wordLibrary/WordLibraryCore';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// 简化的Store接口用于测试
interface TestStore extends WordLibraryState {
  validateInput: (libraryKey: WordLibraryKey, text: string) => { isValid: boolean; errors: string[] };
  checkCrossLibraryDuplicate: (text: string) => { isDuplicate: boolean; duplicateLibraries: WordLibraryKey[] };
  addWord: (libraryKey: WordLibraryKey, text: string) => any;
  removeWord: (libraryKey: WordLibraryKey, wordId: string) => boolean;
  setWordHighlightColor: (word: string, color: string) => void;
  getWordHighlightColor: (word: string) => string | undefined;
}

// 创建测试Store
const createTestStore = () => create<TestStore>()((set, get) => ({
  ...createInitialWordLibraryState(),
  
  validateInput: (libraryKey: WordLibraryKey, text: string) => {
    const state = get();
    return validateInputBeforeSubmit(text, libraryKey, state.globalWordIndex);
  },
  
  checkCrossLibraryDuplicate: (text: string) => {
    const state = get();
    return detectCrossLibraryDuplicates(text, state.globalWordIndex);
  },
  
  addWord: (libraryKey: WordLibraryKey, text: string) => {
    // 使用新的输入验证（阻止性）
    const inputValidation = get().validateInput(libraryKey, text);
    
    if (!inputValidation.isValid) {
      // 输入验证失败，直接返回错误，不录入数据
      return {
        isValid: false,
        errors: inputValidation.errors,
        isDuplicate: false,
        duplicateLibraries: []
      };
    }

    // 输入验证通过，直接录入数据
    const trimmedText = text.trim();
    let addedSuccessfully = false;

    set(produce((draft) => {
      const library = draft.libraries.get(libraryKey);
      if (library) {
        const wordEntry = createWordEntry(trimmedText, library.color, library.level);
        library.words.push(wordEntry);
        library.lastUpdated = new Date();
        
        // 同步更新全局索引
        addWordToGlobalIndex(draft.globalWordIndex, trimmedText, libraryKey);
        
        addedSuccessfully = true;
      }
    }));

    if (addedSuccessfully) {
      // 检查跨词库重复并更新高亮
      const crossLibraryCheck = get().checkCrossLibraryDuplicate(trimmedText);
      
      if (crossLibraryCheck.isDuplicate) {
        // 为跨词库重复词语分配高亮颜色
        const currentColor = get().getWordHighlightColor(trimmedText);
        if (!currentColor) {
          const newColor = assignWordHighlightColor(get().usedHighlightColors);
          get().setWordHighlightColor(trimmedText, newColor);
        }
      }

      return {
        isValid: true,
        errors: [],
        isDuplicate: crossLibraryCheck.isDuplicate,
        duplicateLibraries: crossLibraryCheck.duplicateLibraries
      };
    }

    return {
      isValid: false,
      errors: ['添加词语失败'],
      isDuplicate: false,
      duplicateLibraries: []
    };
  },
  
  removeWord: (libraryKey: WordLibraryKey, wordId: string) => {
    let removed = false;
    let removedWordText = '';

    set(produce((draft) => {
      const library = draft.libraries.get(libraryKey);
      if (library) {
        const wordIndex = library.words.findIndex(word => word.id === wordId);
        if (wordIndex !== -1) {
          removedWordText = library.words[wordIndex].text;
          library.words.splice(wordIndex, 1);
          library.lastUpdated = new Date();
          
          // 同步更新全局索引
          removeWordFromGlobalIndex(draft.globalWordIndex, removedWordText, libraryKey);
          
          removed = true;
        }
      }
    }));

    return removed;
  },
  
  setWordHighlightColor: (word: string, color: string) => {
    set(produce((draft) => {
      draft.wordHighlightColors.set(word, color);
      draft.usedHighlightColors.add(color);
    }));
  },
  
  getWordHighlightColor: (word: string) => {
    return get().wordHighlightColors.get(word);
  }
}));

// Bug修复测试
function runBugFixTests() {
  console.log('🚀 开始Bug修复验证测试\n');
  
  const useStore = createTestStore();
  
  console.log('📋 测试1: 修复同一词库重复录入问题');
  console.log('---');
  
  // 添加第一个词语
  let result1 = useStore.getState().addWord('black-1', '测试');
  console.log('添加"测试"到黑1:', result1.isValid ? '✅ 成功' : '❌ 失败');
  
  // 尝试再次添加相同词语（应该被阻止）
  let result2 = useStore.getState().addWord('black-1', '测试');
  console.log('再次添加"测试"到黑1:', !result2.isValid ? '✅ 正确阻止' : '❌ 错误允许');
  console.log('错误信息:', result2.errors);
  
  // 检查词库中的词语数量
  const blackLibrary = useStore.getState().libraries.get('black-1');
  const wordCount = blackLibrary?.words.length || 0;
  console.log(`黑1词库词语数量: ${wordCount} (应该是2，包括默认的"主题")`);
  
  console.log('\n📋 测试2: 修复Immer冻结对象错误');
  console.log('---');
  
  try {
    // 添加跨词库重复词语，测试高亮颜色分配
    let result3 = useStore.getState().addWord('red-1', '测试');
    console.log('添加"测试"到红1:', result3.isValid ? '✅ 成功' : '❌ 失败');
    console.log('是否标记为重复:', result3.isDuplicate ? '✅ 是' : '❌ 否');
    
    // 检查高亮颜色是否正确分配
    const highlightColor = useStore.getState().getWordHighlightColor('测试');
    console.log('高亮颜色分配:', highlightColor ? `✅ ${highlightColor}` : '❌ 未分配');
    
    console.log('✅ Immer冻结对象错误已修复');
  } catch (error) {
    console.log('❌ Immer冻结对象错误仍存在:', error);
  }
  
  console.log('\n📋 测试3: 删除操作测试');
  console.log('---');
  
  const state = useStore.getState();
  const testWord = state.libraries.get('black-1')?.words.find(w => w.text === '测试');
  
  if (testWord) {
    console.log('删除前词语数量:', state.libraries.get('black-1')?.words.length);
    
    const removed = useStore.getState().removeWord('black-1', testWord.id);
    console.log('删除操作结果:', removed ? '✅ 成功' : '❌ 失败');
    
    const afterState = useStore.getState();
    console.log('删除后词语数量:', afterState.libraries.get('black-1')?.words.length);
    
    // 检查全局索引是否正确更新
    const indexCheck = afterState.globalWordIndex.get('测试');
    const hasBlackLibrary = indexCheck?.has('black-1') || false;
    console.log('全局索引更新:', !hasBlackLibrary ? '✅ 正确' : '❌ 错误');
  }
  
  console.log('\n📋 测试4: 格式验证测试');
  console.log('---');
  
  const formatTests = [
    { word: '短', expected: '太短' },
    { word: '这是一个超长的词语', expected: '太长' },
    { word: 'test', expected: '非中文' },
    { word: '测试！', expected: '包含标点' }
  ];
  
  formatTests.forEach(test => {
    const result = useStore.getState().addWord('blue-1', test.word);
    console.log(`"${test.word}" (${test.expected}):`, !result.isValid ? '✅ 正确阻止' : '❌ 错误允许');
  });
  
  console.log('\n✨ Bug修复验证测试完成');
}

// 运行测试
if (require.main === module) {
  runBugFixTests();
}

export { runBugFixTests };
