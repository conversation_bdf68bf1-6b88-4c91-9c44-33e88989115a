/**
 * 测试词语模式字体大小修改
 * 验证在【颜色】【词语】模式下，字体大小是否正确设置为9px
 */

import { renderCellByMode } from '../core/matrix/MatrixCore';
import type { CellData } from '../core/matrix/MatrixTypes';

// 模拟单元格数据
const mockCell: CellData = {
  x: 5,
  y: 10,
  isActive: true,
  isSelected: false,
  isHovered: false,
  color: 'red',
  level: 2,
  value: 2,
};

console.log('🧪 测试词语模式字体大小修改');
console.log('=====================================');

// 测试各种模式的字体大小
const testCases = [
  { mode: 'coordinate', expected: '9px', description: '坐标模式' },
  { mode: 'index', expected: '9px', description: '索引模式' },
  { mode: 'word', expected: '9px', description: '词语模式' },
  { mode: 'level', expected: '16px', description: '等级模式' },
  { mode: 'mapping', expected: '16px', description: '映射模式' },
  { mode: 'blank', expected: '16px', description: '空白模式' },
] as const;

let allTestsPassed = true;

testCases.forEach(({ mode, expected, description }) => {
  try {
    const result = renderCellByMode(mockCell, 'color', mode as any);
    const actualFontSize = result.style.fontSize;
    const passed = actualFontSize === expected;
    
    console.log(`${passed ? '✅' : '❌'} ${description}: ${actualFontSize} (期望: ${expected})`);
    
    if (!passed) {
      allTestsPassed = false;
    }
  } catch (error) {
    console.log(`❌ ${description}: 测试失败 - ${error}`);
    allTestsPassed = false;
  }
});

console.log('=====================================');
console.log(`🎯 测试结果: ${allTestsPassed ? '全部通过' : '存在失败'}`);

if (allTestsPassed) {
  console.log('✨ 词语模式字体大小修改成功！');
  console.log('📝 现在词语模式使用9px字体，与坐标模式保持一致');
} else {
  console.log('⚠️  存在测试失败，请检查代码修改');
}
