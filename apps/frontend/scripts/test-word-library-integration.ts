/**
 * 词库集成测试脚本
 * 🎯 测试目标：模拟真实的词库操作场景，验证完整的查重逻辑
 * 📦 测试范围：Store操作、高亮颜色管理、批量操作
 */

import { create } from 'zustand';
import { enableMapSet, produce } from 'immer';
import type { WordLibraryState, WordLibraryKey } from '../core/matrix/MatrixTypes';
import { 
  createInitialWordLibraryState,
  createWordEntry,
  validateWord,
  updateDuplicateWordsMapWithColors
} from '../core/wordLibrary/WordLibraryCore';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// 简化的Store接口用于测试
interface TestStore extends WordLibraryState {
  addWord: (libraryKey: WordLibraryKey, text: string) => any;
  updateDuplicateMap: () => void;
  setWordHighlightColor: (word: string, color: string) => void;
  getWordHighlightColor: (word: string) => string | undefined;
}

// 创建测试Store
const createTestStore = () => create<TestStore>()((set, get) => ({
  ...createInitialWordLibraryState(),
  
  addWord: (libraryKey: WordLibraryKey, text: string) => {
    const state = get();
    const validation = validateWord(text, libraryKey, state.libraries);

    if (validation.isValid) {
      set(produce((draft) => {
        const library = draft.libraries.get(libraryKey);
        if (library) {
          const wordEntry = createWordEntry(text, library.color, library.level);
          library.words.push(wordEntry);
          library.lastUpdated = new Date();
        }
      }));

      // 更新重复词语映射
      get().updateDuplicateMap();
      
      // 如果是跨词库重复词语，分配高亮颜色
      if (validation.isDuplicate && validation.duplicateLibraries.length > 0) {
        const trimmedText = text.trim();
        const currentColor = get().getWordHighlightColor(trimmedText);
        
        if (!currentColor) {
          // 简化的颜色分配逻辑
          const colors = ['#ffeb3b', '#ff9800', '#e91e63', '#9c27b0'];
          const usedColors = get().usedHighlightColors;
          const availableColors = colors.filter(c => !usedColors.has(c));
          const newColor = availableColors[0] || colors[0];
          get().setWordHighlightColor(trimmedText, newColor);
        }
      }
    }

    return validation;
  },
  
  updateDuplicateMap: () => {
    const state = get();
    const result = updateDuplicateWordsMapWithColors(
      state.libraries,
      state.wordHighlightColors,
      state.usedHighlightColors
    );
    
    set({
      duplicateWords: result.duplicateWords,
      wordHighlightColors: result.wordHighlightColors,
      usedHighlightColors: result.usedHighlightColors
    });
  },
  
  setWordHighlightColor: (word: string, color: string) => {
    set(produce((draft) => {
      draft.wordHighlightColors.set(word, color);
      draft.usedHighlightColors.add(color);
    }));
  },
  
  getWordHighlightColor: (word: string) => {
    return get().wordHighlightColors.get(word);
  }
}));

// 集成测试场景
function runIntegrationTests() {
  console.log('🚀 开始词库集成测试\n');
  
  const useStore = createTestStore();
  
  console.log('📋 场景1: 基础词语添加');
  console.log('---');
  
  // 添加一些基础词语
  let result1 = useStore.getState().addWord('black-1', '编程');
  console.log('添加"编程"到黑1:', result1.isValid ? '✅ 成功' : '❌ 失败');
  
  let result2 = useStore.getState().addWord('red-1', '开发');
  console.log('添加"开发"到红1:', result2.isValid ? '✅ 成功' : '❌ 失败');
  
  let result3 = useStore.getState().addWord('blue-1', '测试');
  console.log('添加"测试"到蓝1:', result3.isValid ? '✅ 成功' : '❌ 失败');
  
  console.log('\n📋 场景2: 同一词库内重复检测');
  console.log('---');
  
  // 尝试在同一词库内添加重复词语
  let result4 = useStore.getState().addWord('black-1', '编程');
  console.log('再次添加"编程"到黑1:', !result4.isValid ? '✅ 正确拒绝' : '❌ 错误允许');
  console.log('错误信息:', result4.errors);
  
  console.log('\n📋 场景3: 跨词库重复检测和高亮');
  console.log('---');
  
  // 在不同词库中添加相同词语
  let result5 = useStore.getState().addWord('red-1', '编程');
  console.log('添加"编程"到红1:', result5.isValid ? '✅ 允许' : '❌ 错误拒绝');
  console.log('是否标记为重复:', result5.isDuplicate ? '✅ 是' : '❌ 否');
  console.log('重复词库:', result5.duplicateLibraries);
  
  // 检查高亮颜色
  const highlightColor = useStore.getState().getWordHighlightColor('编程');
  console.log('高亮颜色:', highlightColor ? `✅ ${highlightColor}` : '❌ 未分配');
  
  console.log('\n📋 场景4: 多个重复词语的颜色管理');
  console.log('---');
  
  // 添加更多重复词语
  useStore.getState().addWord('green-1', '开发');
  useStore.getState().addWord('yellow-1', '测试');
  useStore.getState().addWord('purple-1', '编程');
  
  // 检查所有高亮颜色
  const state = useStore.getState();
  console.log('重复词语映射:');
  state.duplicateWords.forEach((libraries, word) => {
    const color = state.wordHighlightColors.get(word);
    console.log(`  "${word}": ${libraries.join(', ')} -> ${color}`);
  });
  
  console.log('\n📋 场景5: 格式验证');
  console.log('---');
  
  const invalidTests = [
    { word: '短', expected: '太短' },
    { word: '这是一个超长的词语', expected: '太长' },
    { word: '测试！', expected: '包含标点' },
    { word: 'test', expected: '非中文' },
    { word: '', expected: '空词语' }
  ];
  
  invalidTests.forEach(test => {
    const result = useStore.getState().addWord('black-1', test.word);
    console.log(`"${test.word}" (${test.expected}):`, !result.isValid ? '✅ 正确拒绝' : '❌ 错误允许');
  });
  
  console.log('\n📋 场景6: 统计信息');
  console.log('---');
  
  const finalState = useStore.getState();
  let totalWords = 0;
  finalState.libraries.forEach(library => {
    totalWords += library.words.length;
    console.log(`${library.key}: ${library.words.length} 个词语`);
  });
  
  console.log(`总词语数: ${totalWords}`);
  console.log(`重复词语数: ${finalState.duplicateWords.size}`);
  console.log(`已使用颜色数: ${finalState.usedHighlightColors.size}`);
  
  console.log('\n✨ 集成测试完成');
}

// 运行测试
if (require.main === module) {
  runIntegrationTests();
}

export { runIntegrationTests };
