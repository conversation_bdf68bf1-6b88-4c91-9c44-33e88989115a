/**
 * 词库功能测试脚本
 * 测试1秒延迟检测、查重逻辑、富文本显示和删除功能
 */

// 模拟测试环境
const testResults = [];

function logTest(testName, passed, details = '') {
  testResults.push({
    name: testName,
    passed,
    details,
    timestamp: new Date().toISOString()
  });
  
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: ${testName}`);
  if (details) {
    console.log(`   详情: ${details}`);
  }
}

// 测试1：1秒延迟检测机制
function test1SecondDelayDetection() {
  console.log('\n=== 测试1：1秒延迟检测机制 ===');
  
  // 模拟输入包含逗号的情况
  const inputWithComma = '主题，集合，系统';
  const hasComma = inputWithComma.includes('，') || inputWithComma.includes(',');
  
  logTest(
    '检测输入中的逗号',
    hasComma,
    `输入: "${inputWithComma}", 包含逗号: ${hasComma}`
  );
  
  // 模拟文本切割
  const words = inputWithComma.split(/[，,]/).map(word => word.trim()).filter(word => word.length > 0);
  const expectedWords = ['主题', '集合', '系统'];
  const wordsMatch = JSON.stringify(words) === JSON.stringify(expectedWords);
  
  logTest(
    '文本切割功能',
    wordsMatch,
    `切割结果: ${JSON.stringify(words)}, 期望: ${JSON.stringify(expectedWords)}`
  );
}

// 测试2：查重逻辑
function testDuplicateDetection() {
  console.log('\n=== 测试2：查重逻辑 ===');
  
  // 模拟词库数据
  const mockLibraries = new Map([
    ['black-1', { words: [{ text: '主题' }, { text: '集合' }] }],
    ['red-1', { words: [{ text: '主题' }, { text: '系统' }] }]
  ]);
  
  // 测试同一词库内重复检测
  const sameLibraryDuplicate = mockLibraries.get('black-1').words.some(word => word.text === '主题');
  logTest(
    '同一词库内重复检测',
    sameLibraryDuplicate,
    '在black-1词库中检测到"主题"重复'
  );
  
  // 测试跨词库重复检测
  const crossLibraryDuplicate = Array.from(mockLibraries.values())
    .some(lib => lib.words.some(word => word.text === '主题'));
  logTest(
    '跨词库重复检测',
    crossLibraryDuplicate,
    '在多个词库中检测到"主题"重复'
  );
}

// 测试3：词语格式验证
function testWordValidation() {
  console.log('\n=== 测试3：词语格式验证 ===');
  
  const testCases = [
    { word: '主题', valid: true, reason: '2-4个中文字符' },
    { word: '系统管理', valid: true, reason: '4个中文字符' },
    { word: '主', valid: false, reason: '少于2个字符' },
    { word: '系统管理员', valid: false, reason: '超过4个字符' },
    { word: 'theme', valid: false, reason: '非中文字符' },
    { word: '主题！', valid: false, reason: '包含标点符号' }
  ];
  
  testCases.forEach(testCase => {
    const { word, valid, reason } = testCase;
    
    // 模拟验证逻辑
    const length = word.length;
    const hasChineseChar = /[\u4e00-\u9fff]/.test(word);
    const hasSpecialChar = /[，。！？；：""''（）【】《》]/.test(word);
    const isValidLength = length >= 2 && length <= 4;
    
    const actualValid = isValidLength && hasChineseChar && !hasSpecialChar;
    
    logTest(
      `词语格式验证: "${word}"`,
      actualValid === valid,
      `期望: ${valid ? '有效' : '无效'} (${reason}), 实际: ${actualValid ? '有效' : '无效'}`
    );
  });
}

// 测试4：富文本显示格式
function testRichTextFormat() {
  console.log('\n=== 测试4：富文本显示格式 ===');
  
  // 模拟词库数据
  const mockLibrary = {
    color: 'black',
    level: 1,
    words: [
      { text: '主题', usageCount: 2 },
      { text: '集合', usageCount: 1 },
      { text: '系统', usageCount: 0 }
    ]
  };
  
  // 生成富文本格式
  const displayName = '黑1';
  const totalWords = mockLibrary.words.length;
  const wordList = mockLibrary.words.slice(0, 3).map(word => 
    `${word.text}[${word.usageCount || 0}]`
  ).join('，');
  
  const richText = `【${displayName}[${totalWords}词]：${wordList}】`;
  const expectedFormat = '【黑1[3词]：主题[2]，集合[1]，系统[0]】';
  
  logTest(
    '富文本格式生成',
    richText === expectedFormat,
    `生成: "${richText}", 期望: "${expectedFormat}"`
  );
}

// 测试5：随机颜色生成
function testRandomColorGeneration() {
  console.log('\n=== 测试5：随机颜色生成 ===');
  
  const highlightColors = [
    '#ffeb3b', '#ff9800', '#e91e63', '#9c27b0', 
    '#673ab7', '#3f51b5', '#2196f3', '#00bcd4'
  ];
  
  const usedColors = new Set(['#ffeb3b', '#ff9800']);
  const availableColors = highlightColors.filter(color => !usedColors.has(color));
  
  logTest(
    '可用颜色过滤',
    availableColors.length === highlightColors.length - usedColors.size,
    `总颜色: ${highlightColors.length}, 已用: ${usedColors.size}, 可用: ${availableColors.length}`
  );
  
  // 模拟随机选择
  const randomColor = availableColors[Math.floor(Math.random() * availableColors.length)];
  const isValidColor = highlightColors.includes(randomColor) && !usedColors.has(randomColor);
  
  logTest(
    '随机颜色选择',
    isValidColor,
    `选择的颜色: ${randomColor}, 是否有效: ${isValidColor}`
  );
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始词库功能测试...\n');
  
  test1SecondDelayDetection();
  testDuplicateDetection();
  testWordValidation();
  testRichTextFormat();
  testRandomColorGeneration();
  
  // 生成测试报告
  console.log('\n📊 测试报告');
  console.log('='.repeat(50));
  
  const totalTests = testResults.length;
  const passedTests = testResults.filter(test => test.passed).length;
  const failedTests = totalTests - passedTests;
  
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过: ${passedTests}`);
  console.log(`失败: ${failedTests}`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (failedTests > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.filter(test => !test.passed).forEach(test => {
      console.log(`  - ${test.name}: ${test.details}`);
    });
  }
  
  console.log('\n✨ 测试完成!');
  return { totalTests, passedTests, failedTests };
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests };
} else {
  // 在浏览器环境中运行
  runAllTests();
}
