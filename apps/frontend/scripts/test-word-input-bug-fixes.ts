/**
 * 填词功能Bug修复验证测试脚本
 * 🎯 测试目标：验证所有修复的填词功能bug是否正常工作
 */

import { test, expect } from '@playwright/test';

test.describe('填词功能Bug修复验证', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到应用
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 切换到【颜色】【词语】模式
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setMainMode('color');
      store.setContentMode('word');
    });
    
    // 等待模式切换完成
    await page.waitForTimeout(500);
  });

  test('✅ Bug 1: 点击空白退出填词模式', async ({ page }) => {
    // 添加词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '测试词语');
    });
    
    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 验证填词模式激活
    await expect(cell).toHaveClass(/word-input-active/);
    
    // 点击空白区域（不是词库或单元格）
    await page.click('body', { position: { x: 50, y: 50 } });
    
    // 验证填词模式退出
    await expect(cell).not.toHaveClass(/word-input-active/);
  });

  test('✅ Bug 2: Toast消息正确显示在中央', async ({ page }) => {
    // 清空词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.clearAllLibraries();
    });
    
    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 验证Toast消息显示
    const toastMessage = page.locator('text=请填入词语');
    await expect(toastMessage).toBeVisible();
    
    // 验证Toast在中央显示
    const toastContainer = page.locator('[data-toast]');
    await expect(toastContainer).toBeVisible();
    
    // 等待2秒后验证Toast消失
    await page.waitForTimeout(2100);
    await expect(toastMessage).not.toBeVisible();
  });

  test('✅ Bug 3: 智能定位到已绑定词语', async ({ page }) => {
    // 添加多个词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore, useMatrixStore } = require('@/core/wordLibrary/WordLibraryStore');
      const wordStore = useWordLibraryStore.getState();
      const matrixStore = useMatrixStore.getState();
      
      const word1Id = wordStore.addWord('red-1', '第一个词');
      const word2Id = wordStore.addWord('red-1', '第二个词');
      const word3Id = wordStore.addWord('red-1', '第三个词');
      
      // 绑定第二个词到单元格
      matrixStore.bindWordToCell(5, 5, word2Id);
    });
    
    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 验证填词模式激活
    await expect(cell).toHaveClass(/word-input-active/);
    
    // 验证显示的是已绑定的词语（第二个词）
    await expect(cell).toContainText('第二个词');
  });

  test('✅ Bug 4: 临时显示词语只在【颜色】【词语】模式下生效', async ({ page }) => {
    // 添加词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '临时词语');
    });
    
    // 先在【颜色】【词语】模式下测试
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 验证临时显示生效
    await expect(cell).toContainText('临时词语');
    
    // 退出填词模式
    await page.keyboard.press('Escape');
    
    // 切换到其他模式
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setMainMode('default');
    });
    
    // 再次双击，应该不会显示临时词语
    await cell.dblclick();
    
    // 验证不会显示临时词语（因为不在【颜色】【词语】模式）
    const cellContent = await cell.textContent();
    expect(cellContent?.includes('临时词语')).toBe(false);
  });

  test('✅ Bug 5: 词库滑动到可视区域', async ({ page }) => {
    // 添加词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '测试词语');
    });
    
    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 验证词库高亮显示
    const libraryItem = page.locator('.word-library-active');
    await expect(libraryItem).toBeVisible();
    
    // 验证词库在可视区域内
    const isInViewport = await libraryItem.evaluate(el => {
      const rect = el.getBoundingClientRect();
      return rect.top >= 0 && rect.bottom <= window.innerHeight;
    });
    
    expect(isInViewport).toBe(true);
  });

  test('✅ Bug 6: 左右键实时切换词语', async ({ page }) => {
    // 添加多个词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '词语A');
      store.addWord('red-1', '词语B');
      store.addWord('red-1', '词语C');
    });
    
    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();
    
    // 验证初始显示第一个词语
    await expect(cell).toContainText('词语A');
    
    // 按右键切换到下一个词语
    await page.keyboard.press('ArrowRight');
    await page.waitForTimeout(100);
    await expect(cell).toContainText('词语B');
    
    // 按左键切换到上一个词语
    await page.keyboard.press('ArrowLeft');
    await page.waitForTimeout(100);
    await expect(cell).toContainText('词语A');
  });
});
