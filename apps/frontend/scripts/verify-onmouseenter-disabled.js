/**
 * 验证 onMouseEnter 事件禁用效果的脚本
 * 确认所有可能触发词库滑动的 onMouseEnter 事件已被禁用
 */

console.log('🔧 开始验证 onMouseEnter 事件禁用效果...');

const fs = require('fs');
const path = require('path');

// 检查 WordInput.tsx 中的修改
const wordInputPath = path.join(__dirname, '../components/ui/WordInput.tsx');
const wordInputContent = fs.readFileSync(wordInputPath, 'utf8');

console.log('\n📋 检查 WordInput.tsx 中的 onMouseEnter 禁用:');

// 检查是否禁用了 onMouseEnter 事件
if (wordInputContent.includes('// onMouseEnter={() => setShowTooltip(true)} // 已禁用：防止触发词库滑动')) {
  console.log('✅ WordInput.tsx 中的 onMouseEnter 事件已禁用');
} else {
  console.log('❌ WordInput.tsx 中的 onMouseEnter 事件未禁用');
}

// 检查是否禁用了 onMouseLeave 事件
if (wordInputContent.includes('// onMouseLeave={() => setShowTooltip(false)} // 已禁用：防止触发词库滑动')) {
  console.log('✅ WordInput.tsx 中的 onMouseLeave 事件已禁用');
} else {
  console.log('❌ WordInput.tsx 中的 onMouseLeave 事件未禁用');
}

// 检查是否禁用了工具提示
if (wordInputContent.includes('/* {showTooltip && (isDuplicate || !isValid) && (')) {
  console.log('✅ WordInput.tsx 中的工具提示已禁用');
} else {
  console.log('❌ WordInput.tsx 中的工具提示未禁用');
}

// 检查 Matrix.tsx 中的修改
const matrixPath = path.join(__dirname, '../components/Matrix.tsx');
const matrixContent = fs.readFileSync(matrixPath, 'utf8');

console.log('\n📋 检查 Matrix.tsx 中的 onMouseEnter 禁用:');

// 检查是否禁用了 onMouseEnter 事件
if (matrixContent.includes('// onMouseEnter={handleCellMouseEnter} // 已禁用：防止触发词库滑动')) {
  console.log('✅ Matrix.tsx 中的 onMouseEnter 事件已禁用');
} else {
  console.log('❌ Matrix.tsx 中的 onMouseEnter 事件未禁用');
}

// 检查是否注释了 handleCellMouseEnter 函数
if (matrixContent.includes('// const handleCellMouseEnter = useCallback((event: React.MouseEvent) => {')) {
  console.log('✅ Matrix.tsx 中的 handleCellMouseEnter 函数已注释');
} else {
  console.log('❌ Matrix.tsx 中的 handleCellMouseEnter 函数未注释');
}

// 检查是否注释了 hoverCell 导入
if (matrixContent.includes('// hoverCell, // 已禁用：防止触发词库滑动')) {
  console.log('✅ Matrix.tsx 中的 hoverCell 导入已注释');
} else {
  console.log('❌ Matrix.tsx 中的 hoverCell 导入未注释');
}

// 搜索是否还有其他活跃的 onMouseEnter 事件
console.log('\n📋 搜索其他活跃的 onMouseEnter 事件:');

const componentsDir = path.join(__dirname, '../components');
const searchForActiveOnMouseEnter = (dir) => {
  const files = fs.readdirSync(dir);
  let foundActiveEvents = [];

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      foundActiveEvents = foundActiveEvents.concat(searchForActiveOnMouseEnter(filePath));
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 查找未注释的 onMouseEnter 事件
      const lines = content.split('\n');
      lines.forEach((line, index) => {
        if (line.includes('onMouseEnter') && !line.trim().startsWith('//') && !line.includes('已禁用')) {
          foundActiveEvents.push({
            file: filePath.replace(componentsDir, ''),
            line: index + 1,
            content: line.trim()
          });
        }
      });
    }
  });

  return foundActiveEvents;
};

const activeEvents = searchForActiveOnMouseEnter(componentsDir);

if (activeEvents.length === 0) {
  console.log('✅ 未发现其他活跃的 onMouseEnter 事件');
} else {
  console.log('❌ 发现以下活跃的 onMouseEnter 事件:');
  activeEvents.forEach(event => {
    console.log(`   ${event.file}:${event.line} - ${event.content}`);
  });
}

console.log('\n🎉 验证完成！');

// 输出修改总结
console.log('\n📊 修改总结:');
console.log('1. ✅ 禁用 WordInput.tsx 中的 onMouseEnter/onMouseLeave 事件');
console.log('2. ✅ 禁用 WordInput.tsx 中的工具提示显示');
console.log('3. ✅ 禁用 Matrix.tsx 中的 onMouseEnter 事件');
console.log('4. ✅ 注释 Matrix.tsx 中的 handleCellMouseEnter 函数');
console.log('5. ✅ 注释 Matrix.tsx 中的 hoverCell 导入');

console.log('\n🧪 测试建议:');
console.log('1. 鼠标悬停在词语标签上，确认不会显示工具提示');
console.log('2. 鼠标悬停在矩阵单元格上，确认不会触发悬停效果');
console.log('3. 双击激活填词模式后，移动鼠标确认不会重复触发滑动');
console.log('4. 验证词库滑动只在双击激活时触发一次');
