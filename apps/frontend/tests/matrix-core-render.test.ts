/**
 * MatrixCore渲染功能测试
 * 测试单元格渲染逻辑，包括字体大小、背景色等
 */

import { renderCellByMode } from '@/core/matrix/MatrixCore';
import type { CellData } from '@/core/matrix/MatrixTypes';

// 模拟单元格数据
const mockCell: CellData = {
  x: 5,
  y: 10,
  isActive: true,
  isSelected: false,
  isHovered: false,
  color: 'red',
  level: 2,
  value: 2,
};

describe('MatrixCore 渲染功能', () => {
  describe('字体大小设置', () => {
    it('坐标模式应该使用9px字体', () => {
      const result = renderCellByMode(mockCell, 'default', 'coordinate');
      expect(result.style.fontSize).toBe('9px');
    });

    it('索引模式应该使用9px字体', () => {
      const result = renderCellByMode(mockCell, 'default', 'index');
      expect(result.style.fontSize).toBe('9px');
    });

    it('等级模式应该使用16px字体', () => {
      const result = renderCellByMode(mockCell, 'color', 'level');
      expect(result.style.fontSize).toBe('16px');
    });

    it('映射模式应该使用16px字体', () => {
      const result = renderCellByMode(mockCell, 'color', 'mapping');
      expect(result.style.fontSize).toBe('16px');
    });

    it('词语模式应该使用9px字体', () => {
      const result = renderCellByMode(mockCell, 'color', 'word');
      expect(result.style.fontSize).toBe('9px');
    });

    it('空白模式应该使用16px字体', () => {
      const result = renderCellByMode(mockCell, 'default', 'blank');
      expect(result.style.fontSize).toBe('16px');
    });
  });

  describe('背景色设置', () => {
    it('默认模式应该使用白色背景', () => {
      const result = renderCellByMode(mockCell, 'default', 'coordinate');
      expect(result.style.backgroundColor).toBe('#ffffff');
    });

    it('颜色模式应该使用数据驱动的背景色', () => {
      const result = renderCellByMode(mockCell, 'color', 'level');
      // 背景色应该不是白色（具体颜色取决于数据）
      expect(result.style.backgroundColor).not.toBe('#ffffff');
    });
  });

  describe('内容生成', () => {
    it('空白模式应该生成空内容', () => {
      const result = renderCellByMode(mockCell, 'default', 'blank');
      expect(result.content).toBe('');
    });

    it('索引模式应该生成坐标索引', () => {
      const result = renderCellByMode(mockCell, 'default', 'index');
      expect(result.content).toBe('5,10');
    });

    it('坐标模式应该生成显示坐标', () => {
      const result = renderCellByMode(mockCell, 'default', 'coordinate');
      // 显示坐标应该是转换后的坐标
      expect(result.content).toMatch(/\d+,\d+/);
    });

    it('等级模式应该生成等级信息', () => {
      const result = renderCellByMode(mockCell, 'color', 'level');
      // 等级内容取决于是否有矩阵数据
      expect(typeof result.content).toBe('string');
    });
  });

  describe('CSS类名设置', () => {
    it('应该包含正确的CSS类名', () => {
      const result = renderCellByMode(mockCell, 'default', 'coordinate');
      expect(result.className).toContain('matrix-cell');
      expect(result.className).toContain('default-mode');
      expect(result.className).toContain('coordinate-content');
    });

    it('颜色模式应该包含color-mode类名', () => {
      const result = renderCellByMode(mockCell, 'color', 'level');
      expect(result.className).toContain('color-mode');
      expect(result.className).toContain('level-content');
    });
  });

  describe('样式属性', () => {
    it('应该包含基本样式属性', () => {
      const result = renderCellByMode(mockCell, 'default', 'blank');

      expect(result.style).toHaveProperty('backgroundColor');
      expect(result.style).toHaveProperty('color');
      expect(result.style).toHaveProperty('border');
      expect(result.style).toHaveProperty('borderRadius');
      expect(result.style).toHaveProperty('fontSize');
      expect(result.style).toHaveProperty('fontWeight');
      expect(result.style).toHaveProperty('boxShadow');
      expect(result.style).toHaveProperty('transition');
    });

    it('应该设置正确的边框和圆角', () => {
      const result = renderCellByMode(mockCell, 'default', 'blank');

      expect(result.style.border).toBe('1px solid #e0e0e0');
      expect(result.style.borderRadius).toBe('6px');
      expect(result.style.fontWeight).toBe('bold');
    });
  });

  describe('交互性设置', () => {
    it('应该设置为可交互', () => {
      const result = renderCellByMode(mockCell, 'default', 'blank');
      expect(result.isInteractive).toBe(true);
    });
  });
});
