/**
 * 滑动和Toast修复验证测试脚本
 * 🎯 测试目标：验证词库反复滑动和Toast自动消失的修复
 * 🔧 修复内容：
 *   1. 词汇自动滑动，同时间只触发1次 - 添加全局锁定机制
 *   2. 触发两个toast消息，且不消失 - 修复Toast重复显示和自动消失
 */

import { expect, test } from '@playwright/test';

test.describe('滑动和Toast修复验证', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到应用
    await page.goto('/');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 切换到【颜色】【词语】模式
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setMainMode('color');
      store.setContentMode('word');
    });

    // 等待模式切换完成
    await page.waitForTimeout(500);
  });

  test('🐛 Bug 1: 词库页面不再反复触发滑动', async ({ page }) => {
    // 添加词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '测试词语');
    });

    // 监听滚动事件
    let scrollCount = 0;
    await page.evaluate(() => {
      window.scrollEventCount = 0;
      const originalScrollIntoView = Element.prototype.scrollIntoView;
      Element.prototype.scrollIntoView = function (...args) {
        window.scrollEventCount++;
        console.log('scrollIntoView called:', window.scrollEventCount);
        return originalScrollIntoView.apply(this, args);
      };
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 等待一段时间，观察是否有重复滚动
    await page.waitForTimeout(1000);

    // 检查滚动次数
    const finalScrollCount = await page.evaluate(() => window.scrollEventCount);

    // 应该只滚动一次，不应该反复滚动
    expect(finalScrollCount).toBeLessThanOrEqual(2); // 允许最多2次（可能有初始化滚动）

    console.log(`滚动次数: ${finalScrollCount}`);
  });

  test('🐛 Bug 2: Toast消息自动消失', async ({ page }) => {
    // 清空词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.clearAllLibraries();
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 验证Toast消息显示
    const toastMessage = page.locator('text=请填入词语');
    await expect(toastMessage).toBeVisible();

    console.log('Toast消息已显示');

    // 等待1秒，Toast应该还在显示
    await page.waitForTimeout(1000);
    await expect(toastMessage).toBeVisible();

    console.log('1秒后Toast仍在显示');

    // 等待2.5秒（总共3.5秒），Toast应该已经消失
    await page.waitForTimeout(1500);
    await expect(toastMessage).not.toBeVisible();

    console.log('2秒后Toast已自动消失');
  });

  test('🐛 Bug 3: 词库滑动防抖机制', async ({ page }) => {
    // 添加词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '测试词语1');
      store.addWord('blue-2', '测试词语2');
    });

    // 监听滚动事件
    await page.evaluate(() => {
      window.scrollEvents = [];
      const originalScrollIntoView = Element.prototype.scrollIntoView;
      Element.prototype.scrollIntoView = function (...args) {
        window.scrollEvents.push(Date.now());
        return originalScrollIntoView.apply(this, args);
      };
    });

    // 快速连续激活不同词库
    const cell1 = page.locator('[data-x="5"][data-y="5"]').first();
    const cell2 = page.locator('[data-x="6"][data-y="6"]').first();

    await cell1.dblclick();
    await page.waitForTimeout(50);
    await page.keyboard.press('Escape');

    await cell2.dblclick();
    await page.waitForTimeout(50);
    await page.keyboard.press('Escape');

    // 等待防抖完成
    await page.waitForTimeout(300);

    // 检查滚动事件的时间间隔
    const scrollEvents = await page.evaluate(() => window.scrollEvents);

    if (scrollEvents.length > 1) {
      const timeDiff = scrollEvents[scrollEvents.length - 1] - scrollEvents[0];
      console.log(`滚动事件时间跨度: ${timeDiff}ms`);

      // 验证防抖机制生效（事件间隔应该大于防抖延迟）
      expect(timeDiff).toBeGreaterThan(100);
    }
  });

  test('🐛 Bug 4: Toast在不同情况下的表现', async ({ page }) => {
    // 测试1: 有词库时不显示Toast
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '测试词语');
    });

    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 验证不显示Toast
    const toastMessage = page.locator('text=请填入词语');
    await expect(toastMessage).not.toBeVisible();

    await page.keyboard.press('Escape');

    // 测试2: 清空词库后显示Toast
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.clearAllLibraries();
    });

    await cell.dblclick();

    // 验证显示Toast
    await expect(toastMessage).toBeVisible();

    // 验证2秒后自动消失
    await page.waitForTimeout(2100);
    await expect(toastMessage).not.toBeVisible();
  });

  test('🔧 修复验证: 滑动锁定机制防止重复滑动', async ({ page }) => {
    // 添加多个词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.clearAllLibraries();
      store.addWord('red-1', '红色词语');
      store.addWord('blue-2', '蓝色词语');
    });

    // 监听控制台日志
    const scrollLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('滑动操作已锁定') || msg.text().includes('执行词库滑动')) {
        scrollLogs.push(msg.text());
      }
    });

    // 快速连续激活不同词库
    const cell1 = page.locator('[data-x="5"][data-y="5"]').first();
    const cell2 = page.locator('[data-x="6"][data-y="6"]').first();

    await cell1.dblclick();
    await page.waitForTimeout(50);
    await page.keyboard.press('Escape');

    await cell2.dblclick();
    await page.waitForTimeout(50);
    await page.keyboard.press('Escape');

    // 等待所有操作完成
    await page.waitForTimeout(1500);

    // 验证锁定机制工作
    console.log('滑动日志:', scrollLogs);
    const lockLogs = scrollLogs.filter(log => log.includes('滑动操作已锁定'));
    const executeLogs = scrollLogs.filter(log => log.includes('执行词库滑动'));

    // 应该有锁定或执行的日志
    expect(lockLogs.length + executeLogs.length).toBeGreaterThan(0);
  });

  test('🔧 修复验证: Toast消息不重复显示', async ({ page }) => {
    // 清空词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.clearAllLibraries();
    });

    // 监听Toast相关日志
    const toastLogs: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('显示Toast') || msg.text().includes('Toast自动隐藏')) {
        toastLogs.push(msg.text());
      }
    });

    const cell = page.locator('[data-x="5"][data-y="5"]').first();

    // 快速多次双击
    await cell.dblclick();
    await page.waitForTimeout(100);
    await cell.dblclick();
    await page.waitForTimeout(100);

    // 验证只有一个Toast显示
    const visibleToasts = page.locator('[data-toast] .fixed:visible');
    const toastCount = await visibleToasts.count();
    expect(toastCount).toBeLessThanOrEqual(1);

    // 验证Toast日志
    console.log('Toast日志:', toastLogs);
    const showLogs = toastLogs.filter(log => log.includes('显示Toast'));

    // 应该有显示日志，但不应该过多
    expect(showLogs.length).toBeGreaterThan(0);
    expect(showLogs.length).toBeLessThanOrEqual(3); // 允许一些重复，但不应该过多
  });
});
